#!/usr/bin/env python3
"""
Simple script to run the Weather Agent A2A Client.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from weather_agent.client.weather_client import WeatherClientCLI

async def main():
    """Main entry point for the client."""
    print("🌍 Starting Weather Agent A2A Client...")
    print("🤖 Connecting to Gemini-powered Weather Agent")
    print()

    try:
        cli = WeatherClientCLI()

        # Check if examples flag is passed
        if len(sys.argv) > 1 and sys.argv[1] == "examples":
            await cli.run_examples()
        else:
            await cli.run_interactive()

    except KeyboardInterrupt:
        print("\n👋 Client stopped by user")
    except Exception as e:
        print(f"\n❌ Client error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
