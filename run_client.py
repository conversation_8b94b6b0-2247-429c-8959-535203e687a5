#!/usr/bin/env python3
"""Entry point for running the Weather Agent client."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from weather_agent.client import main

if __name__ == "__main__":
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Run the client
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\nClient shutdown requested")
    except Exception as e:
        print(f"Client failed: {e}")
        sys.exit(1)
