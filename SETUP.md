# Weather Agent Setup Guide

Quick setup guide for the Weather Agent A2A Server with Google Gemini AI integration.

## 🚀 Quick Setup

### 1. Prerequisites

- Python 3.10 or higher
- Google API Key from [Google AI Studio](https://makersuite.google.com/app/apikey)

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

Create a `.env` file from the example:

```bash
cp .env.example .env
```

Edit `.env` and add your Google API key:

```env
GOOGLE_API_KEY=your_google_api_key_here
```

### 4. Start the Server

```bash
python run_server.py
```

The server will start on `http://127.0.0.1:8001`

### 5. Test with Client

In a new terminal:

```bash
# Interactive client
python run_client.py

# Or run examples
python run_client.py examples
```

## 🔧 Configuration Options

### Environment Variables

```env
# Required
GOOGLE_API_KEY=your_google_api_key_here

# Optional Server Settings
WEATHER_AGENT_HOST=127.0.0.1
WEATHER_AGENT_PORT=8001
WEATHER_AGENT_NAME=weather_agent
LOG_LEVEL=INFO

# Optional A2A Settings
A2A_AGENT_CARD_URL=/.well-known/agent.json
A2A_RPC_URL=/a2a/weather_agent
```

### Command Line Options

```bash
# Server with custom settings
python -m weather_agent.main server --host 0.0.0.0 --port 8002 --log-level DEBUG

# Client with examples
python -m weather_agent.main client --examples

# Show agent info
python -m weather_agent.main info
```

## 🌐 API Endpoints

Once running, access:

- **Agent Card**: http://127.0.0.1:8001/.well-known/agent.json
- **A2A RPC**: http://127.0.0.1:8001/a2a/weather_agent
- **Health Check**: http://127.0.0.1:8001/health
- **API Docs**: http://127.0.0.1:8001/docs

## 💬 Example Queries

Try these weather queries:

```
• "What's the weather like in San Francisco?"
• "Will it rain tomorrow in New York?"
• "What's the forecast for London this weekend?"
• "Should I bring an umbrella to Seattle?"
```

## 🐛 Troubleshooting

### Common Issues

1. **Missing Google API Key**
   ```
   Error: GOOGLE_API_KEY environment variable is required
   ```
   Solution: Add your API key to the `.env` file

2. **Port Already in Use**
   ```
   Error: [Errno 48] Address already in use
   ```
   Solution: Use a different port with `--port 8002`

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'weather_agent'
   ```
   Solution: Install dependencies with `pip install -r requirements.txt`

### Getting Help

1. Check the [full documentation](WEATHER_AGENT_README.md)
2. Run `python -m weather_agent.main info` for configuration details
3. Enable debug logging with `--log-level DEBUG`

## 🎯 Next Steps

1. **Customize the Agent**: Modify `weather_agent/server/weather_agent.py`
2. **Add Real Weather Data**: Integrate with a weather API in `weather_agent/server/weather_agent.py`
3. **Deploy**: Use Docker or cloud platforms for production deployment
4. **Integrate**: Connect with other A2A agents for multi-agent workflows

---

**Ready to build intelligent weather agents! 🌤️**
