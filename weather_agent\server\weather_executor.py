"""
Custom Weather Agent Executor for A2A protocol integration.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any

from a2a.server.agent_execution import AgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.types import (
    Message, Role, TaskState, TaskStatus, TaskStatusUpdateEvent, 
    TextPart, Part
)

from .weather_agent import create_weather_agent
from ..config.settings import settings

logger = logging.getLogger(__name__)


class WeatherAgentExecutor(AgentExecutor):
    """
    Custom AgentExecutor that integrates Google's Gemini AI with A2A protocol
    for weather-related queries.
    """
    
    def __init__(self):
        """Initialize the Weather Agent Executor."""
        super().__init__()
        self._agent = None
        self._initialized = False
        
    async def _ensure_initialized(self):
        """Ensure the agent is initialized."""
        if not self._initialized:
            try:
                self._agent = create_weather_agent()
                self._initialized = True
                logger.info("Weather agent initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize weather agent: {e}")
                raise
    
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        """
        Execute the weather agent's logic for a given request context.
        
        Args:
            context: The request context containing the message, task ID, etc.
            event_queue: The queue to publish events to.
        """
        if not context.message:
            raise ValueError("A2A request must have a message")
        
        task_id = context.task_id
        context_id = context.context_id
        
        try:
            # Ensure agent is initialized
            await self._ensure_initialized()
            
            # Extract user message from context
            user_message = self._extract_user_message(context.message)
            if not user_message:
                raise ValueError("No valid text message found in request")
            
            logger.info(f"Processing weather query: {user_message}")
            
            # Publish working status
            await self._publish_status_update(
                event_queue, task_id, context_id, TaskState.working,
                "Processing your weather request..."
            )
            
            # Process the weather query using Gemini AI
            response = await self._process_weather_query(user_message)
            
            # Publish the response
            await self._publish_status_update(
                event_queue, task_id, context_id, TaskState.completed,
                response, final=True
            )
            
            logger.info(f"Successfully processed weather query for task {task_id}")
            
        except Exception as e:
            logger.error(f"Error processing weather request: {e}", exc_info=True)
            
            # Publish failure status
            error_message = f"I apologize, but I encountered an error while processing your weather request: {str(e)}"
            await self._publish_status_update(
                event_queue, task_id, context_id, TaskState.failed,
                error_message, final=True
            )
    
    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        """
        Cancel an ongoing weather request.
        
        Args:
            context: The request context containing the task ID to cancel.
            event_queue: The queue to publish the cancellation status update to.
        """
        task_id = context.task_id
        context_id = context.context_id
        
        logger.info(f"Cancelling weather request for task {task_id}")
        
        await self._publish_status_update(
            event_queue, task_id, context_id, TaskState.canceled,
            "Weather request has been cancelled.", final=True
        )
    
    def _extract_user_message(self, message: Message) -> Optional[str]:
        """Extract text content from the A2A message."""
        if not message.parts:
            return None
        
        text_parts = []
        for part in message.parts:
            if hasattr(part, 'root') and isinstance(part.root, TextPart):
                text_parts.append(part.root.text)
            elif isinstance(part, TextPart):
                text_parts.append(part.text)
        
        return " ".join(text_parts) if text_parts else None
    
    async def _process_weather_query(self, user_message: str) -> str:
        """
        Process the weather query using the Gemini-powered weather agent.
        
        Args:
            user_message: The user's weather query
            
        Returns:
            The agent's response to the weather query
        """
        try:
            # For this implementation, we'll simulate the agent processing
            # In a full implementation, you would integrate with the ADK agent
            
            # Determine query type and extract location
            query_lower = user_message.lower()
            
            if any(word in query_lower for word in ['current', 'now', 'today', "what's"]):
                # Current weather query
                location = self._extract_location(user_message)
                if location:
                    return await self._get_current_weather_response(location)
                else:
                    return "I'd be happy to help you with current weather information! Could you please specify which location you'd like to know about?"
            
            elif any(word in query_lower for word in ['forecast', 'tomorrow', 'weekend', 'week']):
                # Forecast query
                location = self._extract_location(user_message)
                if location:
                    return await self._get_forecast_response(location)
                else:
                    return "I can provide you with a weather forecast! Please let me know which location you're interested in."
            
            else:
                # General weather query
                return await self._get_general_weather_response(user_message)
                
        except Exception as e:
            logger.error(f"Error processing weather query: {e}")
            return "I apologize, but I'm having trouble processing your weather request right now. Please try again in a moment."
    
    def _extract_location(self, message: str) -> Optional[str]:
        """Extract location from user message (simplified implementation)."""
        # This is a simplified location extraction
        # In production, you'd use more sophisticated NLP
        
        common_prepositions = ['in', 'for', 'at', 'near', 'around']
        words = message.split()
        
        for i, word in enumerate(words):
            if word.lower() in common_prepositions and i + 1 < len(words):
                # Take the next 1-3 words as potential location
                location_parts = []
                for j in range(i + 1, min(i + 4, len(words))):
                    if words[j].replace(',', '').replace('?', '').replace('.', ''):
                        location_parts.append(words[j].replace(',', '').replace('?', '').replace('.', ''))
                    else:
                        break
                if location_parts:
                    return ' '.join(location_parts)
        
        # Fallback: look for capitalized words (potential city names)
        capitalized_words = [word.replace(',', '').replace('?', '') for word in words if word[0].isupper() and len(word) > 2]
        if capitalized_words:
            return ' '.join(capitalized_words[:2])  # Take first 1-2 capitalized words
        
        return None
    
    async def _get_current_weather_response(self, location: str) -> str:
        """Get current weather response for a location."""
        # Mock current weather response
        return f"""🌤️ **Current Weather in {location}**

Temperature: 22°C (72°F)
Condition: Partly cloudy with light winds
Humidity: 65%
Wind: 10 km/h from the southwest
Visibility: 15 km

It's a pleasant day in {location}! Perfect weather for outdoor activities. The partly cloudy skies provide nice relief from direct sunlight."""
    
    async def _get_forecast_response(self, location: str) -> str:
        """Get weather forecast response for a location."""
        # Mock forecast response
        return f"""📅 **3-Day Weather Forecast for {location}**

**Today**: Partly cloudy, High 22°C, Low 15°C
Chance of rain: 20%

**Tomorrow**: Sunny, High 24°C, Low 16°C  
Chance of rain: 10%

**Day After**: Mostly cloudy, High 20°C, Low 14°C
Chance of rain: 40%

Overall, expect pleasant weather over the next few days in {location}, with temperatures staying comfortable and only a slight chance of rain later in the period."""
    
    async def _get_general_weather_response(self, message: str) -> str:
        """Get general weather response."""
        return """🌍 **Weather Assistant Ready!**

I'm here to help you with weather information! I can provide:

• Current weather conditions for any location
• Multi-day weather forecasts  
• Weather advice and recommendations

Just ask me something like:
• "What's the weather like in San Francisco?"
• "Will it rain tomorrow in New York?"
• "What's the forecast for London this weekend?"

What would you like to know about the weather?"""
    
    async def _publish_status_update(
        self, 
        event_queue: EventQueue, 
        task_id: str, 
        context_id: str, 
        state: TaskState, 
        message_text: str,
        final: bool = False
    ) -> None:
        """Publish a task status update to the event queue."""
        
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.agent,
            parts=[Part(root=TextPart(text=message_text))]
        )
        
        status_update = TaskStatusUpdateEvent(
            taskId=task_id,
            status=TaskStatus(
                state=state,
                timestamp=datetime.now(timezone.utc).isoformat(),
                message=message
            ),
            contextId=context_id,
            final=final
        )
        
        await event_queue.enqueue_event(status_update)
