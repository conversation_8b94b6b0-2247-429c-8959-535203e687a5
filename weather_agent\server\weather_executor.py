"""
Simple Weather Agent Executor using Google ADK Gemini agent.
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Optional

from a2a.server.agent_execution import AgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.types import (
    Message, Role, TaskState, TaskStatus, TaskStatusUpdateEvent,
    TextPart, Part
)

from .weather_agent import create_weather_agent

logger = logging.getLogger(__name__)


class WeatherAgentExecutor(AgentExecutor):
    """Simple AgentExecutor that uses Google ADK Gemini agent for weather queries."""

    def __init__(self):
        """Initialize the Weather Agent Executor."""
        super().__init__()
        self._agent = None

    async def _get_agent(self):
        """Get or create the Gemini weather agent."""
        if self._agent is None:
            self._agent = create_weather_agent()
        return self._agent
    
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        """Execute weather query using simple Gemini agent."""
        if not context.message:
            raise ValueError("A2A request must have a message")

        try:
            # Get user message
            user_message = self._extract_user_message(context.message)
            if not user_message:
                raise ValueError("No text message found")

            logger.info(f"Weather query: {user_message}")

            # Get Gemini agent and send message
            agent = await self._get_agent()
            response = await agent.send_message(user_message)

            # Publish response
            await self._publish_response(event_queue, context, response.text)

        except Exception as e:
            logger.error(f"Weather agent error: {e}")
            await self._publish_response(
                event_queue, context,
                f"Sorry, I encountered an error: {str(e)}"
            )
    
    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        """Cancel weather request."""
        await self._publish_response(event_queue, context, "Request cancelled.")
    
    def _extract_user_message(self, message: Message) -> Optional[str]:
        """Extract text content from the A2A message."""
        if not message.parts:
            return None

        text_parts = []
        for part in message.parts:
            if hasattr(part, 'root') and isinstance(part.root, TextPart):
                text_parts.append(part.root.text)
            elif isinstance(part, TextPart):
                text_parts.append(part.text)

        return " ".join(text_parts) if text_parts else None

    async def _publish_response(self, event_queue: EventQueue, context: RequestContext, response_text: str) -> None:
        """Publish response to event queue."""
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.agent,
            parts=[Part(root=TextPart(text=response_text))]
        )

        status_update = TaskStatusUpdateEvent(
            taskId=context.task_id or str(uuid.uuid4()),
            status=TaskStatus(
                state=TaskState.completed,
                timestamp=datetime.now(timezone.utc).isoformat(),
                message=message
            ),
            contextId=context.context_id or str(uuid.uuid4()),
            final=True
        )

        await event_queue.enqueue_event(status_update)

