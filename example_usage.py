#!/usr/bin/env python3
"""Example usage of the Weather Agent programmatically."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from weather_agent.client import WeatherAgentClient
from weather_agent.config import config


async def example_weather_queries():
    """Example of how to use the Weather Agent programmatically."""
    
    # Initialize the client
    agent_url = f"http://{config.host}:{config.port}"
    client = WeatherAgentClient(agent_url)
    
    try:
        # Connect to the agent
        await client.initialize()
        print(f"✅ Connected to {client.agent_card.name}")
        print(f"   Description: {client.agent_card.description}")
        print(f"   Version: {client.agent_card.version}")
        print()
        
        # Example queries
        queries = [
            "What's the weather like in Tokyo?",
            "Should I bring an umbrella today?",
            "What causes lightning during thunderstorms?",
            "What's the best weather for outdoor photography?",
            "How do I dress for 10°C weather?"
        ]
        
        print("🌤️  Sending weather queries to the agent:")
        print("=" * 50)
        
        for i, query in enumerate(queries, 1):
            print(f"\n{i}. Query: {query}")
            print("-" * 40)
            
            try:
                # Send the query and get response
                response = await client.send_weather_query(query)
                print(f"Response: {response}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print("\n" + "=" * 50)
        print("✅ Example completed successfully!")
        
    except Exception as e:
        print(f"❌ Failed to connect to weather agent: {e}")
        print("Make sure the server is running with: python run_server.py")
        
    finally:
        # Clean up
        await client.close()


async def example_with_authentication():
    """Example of using the Weather Agent with API key authentication."""
    
    # Example API key (in real usage, this would come from environment or config)
    api_key = "your-api-key-here"  # Replace with actual API key if needed
    
    agent_url = f"http://{config.host}:{config.port}"
    client = WeatherAgentClient(agent_url, api_key=api_key)
    
    try:
        await client.initialize()
        print(f"✅ Connected with authentication to {client.agent_card.name}")
        
        # Send an authenticated query
        response = await client.send_weather_query("What's the weather forecast for this week?")
        print(f"Response: {response}")
        
    except Exception as e:
        print(f"❌ Authentication example failed: {e}")
        
    finally:
        await client.close()


async def example_batch_queries():
    """Example of sending multiple queries efficiently."""
    
    agent_url = f"http://{config.host}:{config.port}"
    client = WeatherAgentClient(agent_url)
    
    try:
        await client.initialize()
        print(f"✅ Connected for batch queries to {client.agent_card.name}")
        
        # Batch of weather queries
        batch_queries = [
            "Weather in New York",
            "Weather in London", 
            "Weather in Sydney",
            "Weather in Mumbai",
            "Weather in São Paulo"
        ]
        
        print(f"\n🌍 Sending {len(batch_queries)} queries in batch:")
        
        # Send all queries concurrently (be mindful of rate limits)
        tasks = []
        for query in batch_queries:
            task = client.send_weather_query(query)
            tasks.append(task)
        
        # Wait for all responses
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Display results
        for query, response in zip(batch_queries, responses):
            print(f"\n📍 {query}:")
            if isinstance(response, Exception):
                print(f"   ❌ Error: {response}")
            else:
                # Truncate long responses for display
                display_response = response[:200] + "..." if len(response) > 200 else response
                print(f"   {display_response}")
        
    except Exception as e:
        print(f"❌ Batch query example failed: {e}")
        
    finally:
        await client.close()


async def main():
    """Main example function."""
    print("🌤️  Weather Agent - Example Usage")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Check if server is running
    try:
        import httpx
        async with httpx.AsyncClient() as http_client:
            response = await http_client.get(f"http://{config.host}:{config.port}/.well-known/agent.json", timeout=5)
            if response.status_code != 200:
                print("⚠️  Server may not be responding correctly")
    except Exception:
        print("❌ Cannot connect to server. Make sure it's running with:")
        print("   python run_server.py")
        return
    
    # Run examples
    examples = [
        ("Basic Weather Queries", example_weather_queries),
        ("Batch Queries", example_batch_queries),
        # Uncomment to test authentication
        # ("Authentication Example", example_with_authentication),
    ]
    
    for example_name, example_func in examples:
        print(f"\n{'='*20} {example_name} {'='*20}")
        try:
            await example_func()
        except Exception as e:
            print(f"❌ Example {example_name} failed: {e}")
        
        print()  # Add spacing between examples
    
    print("🎉 All examples completed!")
    print("\nTo run the interactive client, use: python run_client.py")


if __name__ == "__main__":
    asyncio.run(main())
