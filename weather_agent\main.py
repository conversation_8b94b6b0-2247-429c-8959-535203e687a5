#!/usr/bin/env uv run python
"""
Main entry point for the Weather Agent A2A Server.
"""

import asyncio
import sys
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from weather_agent.server.app import run_server
from weather_agent.client.weather_client import WeatherClientCLI
from weather_agent.config.settings import settings


def main():
    """Main entry point with command-line argument parsing."""
    
    parser = argparse.ArgumentParser(
        description="Weather Agent A2A Server - Powered by Google's Gemini AI"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Server command
    server_parser = subparsers.add_parser("server", help="Start the weather agent server")
    server_parser.add_argument(
        "--host", 
        default=settings.host,
        help=f"Server host (default: {settings.host})"
    )
    server_parser.add_argument(
        "--port", 
        type=int,
        default=settings.port,
        help=f"Server port (default: {settings.port})"
    )
    server_parser.add_argument(
        "--log-level",
        default=settings.log_level,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help=f"Log level (default: {settings.log_level})"
    )
    
    # Client command
    client_parser = subparsers.add_parser("client", help="Start the weather agent client")
    client_parser.add_argument(
        "--examples",
        action="store_true",
        help="Run example queries instead of interactive mode"
    )
    client_parser.add_argument(
        "--agent-url",
        help="Weather agent URL (default: auto-detect from settings)"
    )
    
    # Info command
    info_parser = subparsers.add_parser("info", help="Show weather agent information")
    
    args = parser.parse_args()
    
    if args.command == "server":
        print(f"🌤️  Starting Weather Agent A2A Server...")
        print(f"📍 Host: {args.host}")
        print(f"🔌 Port: {args.port}")
        print(f"📊 Log Level: {args.log_level}")
        print(f"🤖 AI Model: Gemini 2.0 Flash")
        print()
        
        # Update settings if provided
        if args.host != settings.host:
            settings.host = args.host
        if args.port != settings.port:
            settings.port = args.port
        if args.log_level != settings.log_level:
            settings.log_level = args.log_level
        
        run_server()
        
    elif args.command == "client":
        print(f"🌍 Starting Weather Agent Client...")
        
        async def run_client():
            cli = WeatherClientCLI()
            if args.examples:
                await cli.run_examples()
            else:
                await cli.run_interactive()
        
        asyncio.run(run_client())
        
    elif args.command == "info":
        print_info()
        
    else:
        parser.print_help()


def print_info():
    """Print information about the weather agent."""
    
    print("🌤️  Weather Agent A2A Server")
    print("=" * 50)
    print(f"Version: 1.0.0")
    print(f"AI Model: Google Gemini 2.0 Flash")
    print(f"Protocol: A2A (Agent-to-Agent)")
    print()
    
    print("📋 Configuration:")
    print(f"  Host: {settings.host}")
    print(f"  Port: {settings.port}")
    print(f"  Agent Name: {settings.agent_name}")
    print(f"  Log Level: {settings.log_level}")
    print()
    
    print("🔗 A2A Endpoints:")
    print(f"  Agent Card: http://{settings.host}:{settings.port}{settings.a2a_agent_card_url}")
    print(f"  RPC Endpoint: http://{settings.host}:{settings.port}{settings.a2a_rpc_url}")
    print(f"  Extended Card: http://{settings.host}:{settings.port}{settings.a2a_extended_card_url}")
    print()
    
    print("🎯 Capabilities:")
    print("  • Current weather conditions")
    print("  • Multi-day weather forecasts")
    print("  • Weather-related advice and recommendations")
    print("  • Conversational AI responses")
    print("  • Location-based weather queries")
    print()
    
    print("🚀 Usage:")
    print("  Start server: python -m weather_agent.main server")
    print("  Start client: python -m weather_agent.main client")
    print("  Run examples: python -m weather_agent.main client --examples")
    print()
    
    print("📚 Example Queries:")
    print("  • What's the weather like in San Francisco?")
    print("  • Will it rain tomorrow in New York?")
    print("  • What's the forecast for London this weekend?")
    print("  • Should I bring an umbrella to Tokyo?")


if __name__ == "__main__":
    main()
