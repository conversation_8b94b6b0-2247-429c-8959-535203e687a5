#!/usr/bin/env python3
"""Setup script for the Weather Agent."""

import os
import sys
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_environment_file():
    """Check if .env file exists and has required variables."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  .env file not found. Creating from .env.example...")
            env_file.write_text(env_example.read_text())
            print("✅ Created .env file from template")
        else:
            print("❌ Neither .env nor .env.example found")
            return False
    
    # Check for required variables
    env_content = env_file.read_text()
    if "GOOGLE_API_KEY=your_google_ai_api_key_here" in env_content:
        print("⚠️  Please update GOOGLE_API_KEY in .env file with your actual API key")
        print("   Get your API key at: https://makersuite.google.com/app/apikey")
        return False
    elif "GOOGLE_API_KEY=" not in env_content:
        print("❌ GOOGLE_API_KEY not found in .env file")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    required_modules = [
        "a2a",
        "google.genai",
        "fastapi",
        "httpx",
        "pydantic",
        "structlog",
        "rich"
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError as e:
            print(f"   ❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"❌ Failed to import: {', '.join(failed_imports)}")
        return False
    
    print("✅ All imports successful")
    return True

def main():
    """Main setup function."""
    print("🌤️  Weather Agent Setup")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Environment File", check_environment_file),
        ("Dependencies", install_dependencies),
        ("Import Test", test_imports),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}:")
        if not check_func():
            all_passed = False
            break
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Make sure your GOOGLE_API_KEY is set in .env")
        print("2. Run the server: python run_server.py")
        print("3. In another terminal, run: python run_client.py")
    else:
        print("❌ Setup failed. Please fix the issues above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
