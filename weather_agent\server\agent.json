{"name": "weather_agent", "version": "1.0.0", "description": "An intelligent weather assistant powered by Google's Gemini AI. Provides current weather conditions, forecasts, and weather-related advice for any location worldwide.", "url": "http://127.0.0.1:8001/a2a/weather_agent", "capabilities": {"weather": {"current_conditions": true, "forecasts": true, "multi_day_forecasts": true, "weather_advice": true}, "ai_powered": {"model": "gemini-2.0-flash", "provider": "google", "conversational": true}}, "skills": [{"id": "current_weather", "name": "Current Weather", "description": "Get current weather conditions for any location", "tags": ["weather", "current", "conditions", "temperature"]}, {"id": "weather_forecast", "name": "Weather Forecast", "description": "Get multi-day weather forecasts for any location", "tags": ["weather", "forecast", "prediction", "planning"]}, {"id": "weather_advice", "name": "Weather Advice", "description": "Provide weather-related advice and recommendations", "tags": ["weather", "advice", "recommendations", "planning"]}], "defaultInputModes": ["text/plain"], "defaultOutputModes": ["text/plain", "application/json"], "securitySchemes": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key", "description": "API key for accessing the weather agent"}}}