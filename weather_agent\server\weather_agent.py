"""
Weather Agent implementation using Google's Gemini AI models.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any

from google.adk.agents import Agent
from google.adk.models.google_llm import Gemini
from google.genai import types

from ..config.settings import settings, configure_google_auth

logger = logging.getLogger(__name__)


class WeatherTool:
    """Mock weather tool for demonstration purposes."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        
    async def get_current_weather(self, location: str) -> Dict[str, Any]:
        """Get current weather for a location."""
        # Mock weather data - in production, this would call a real weather API
        mock_weather = {
            "location": location,
            "temperature": "22°C",
            "condition": "Partly cloudy",
            "humidity": "65%",
            "wind_speed": "10 km/h",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Retrieved weather data for {location}: {mock_weather}")
        return mock_weather
    
    async def get_weather_forecast(self, location: str, days: int = 3) -> Dict[str, Any]:
        """Get weather forecast for a location."""
        # Mock forecast data
        forecast_days = []
        for i in range(days):
            day_forecast = {
                "day": f"Day {i+1}",
                "date": datetime.now(timezone.utc).strftime("%Y-%m-%d"),
                "high_temp": f"{20 + i}°C",
                "low_temp": f"{15 + i}°C",
                "condition": "Sunny" if i % 2 == 0 else "Cloudy",
                "precipitation_chance": f"{20 + i*10}%"
            }
            forecast_days.append(day_forecast)
        
        forecast = {
            "location": location,
            "forecast": forecast_days,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Retrieved forecast for {location}: {forecast}")
        return forecast


def create_weather_agent() -> Agent:
    """Create and configure the weather agent with Gemini AI integration."""
    
    # Configure Google authentication
    configure_google_auth()
    
    # Initialize weather tool
    weather_tool = WeatherTool(api_key=settings.weather_api_key)
    
    # Create weather-related functions for the agent
    async def get_current_weather(location: str) -> str:
        """Get current weather information for a specific location."""
        try:
            weather_data = await weather_tool.get_current_weather(location)
            return f"""Current weather in {weather_data['location']}:
- Temperature: {weather_data['temperature']}
- Condition: {weather_data['condition']}
- Humidity: {weather_data['humidity']}
- Wind Speed: {weather_data['wind_speed']}
- Last Updated: {weather_data['timestamp']}"""
        except Exception as e:
            logger.error(f"Error getting weather for {location}: {e}")
            return f"Sorry, I couldn't retrieve weather information for {location}. Please try again later."
    
    async def get_weather_forecast(location: str, days: int = 3) -> str:
        """Get weather forecast for a specific location."""
        try:
            forecast_data = await weather_tool.get_weather_forecast(location, days)
            forecast_text = f"Weather forecast for {forecast_data['location']}:\n\n"
            
            for day in forecast_data['forecast']:
                forecast_text += f"{day['day']} ({day['date']}):\n"
                forecast_text += f"  High: {day['high_temp']}, Low: {day['low_temp']}\n"
                forecast_text += f"  Condition: {day['condition']}\n"
                forecast_text += f"  Precipitation: {day['precipitation_chance']}\n\n"
            
            return forecast_text
        except Exception as e:
            logger.error(f"Error getting forecast for {location}: {e}")
            return f"Sorry, I couldn't retrieve forecast information for {location}. Please try again later."
    
    # Create the weather agent with Gemini model
    weather_agent = Agent(
        name=settings.agent_name,
        model="gemini-2.0-flash",  # Use latest Gemini model
        instruction="""
You are a helpful weather assistant powered by Google's Gemini AI. You provide accurate, 
conversational weather information and forecasts for any location worldwide.

Your capabilities include:
- Current weather conditions for any city or location
- Multi-day weather forecasts
- Weather-related advice and recommendations
- Conversational responses about weather patterns and conditions

When users ask about weather:
1. Always be friendly and conversational
2. Provide specific, detailed weather information
3. Offer helpful context or advice when appropriate
4. If you can't get weather data, apologize and suggest alternatives

Example interactions:
- "What's the weather like in San Francisco?" → Provide current conditions
- "Will it rain tomorrow in New York?" → Check forecast and give probability
- "Should I bring an umbrella to London?" → Check conditions and give advice
- "What's the weather forecast for the weekend in Tokyo?" → Provide multi-day forecast

Always format your responses in a clear, easy-to-read manner and include relevant details
like temperature, conditions, humidity, and wind when available.
        """,
        description="An intelligent weather assistant that provides current weather conditions, forecasts, and weather-related advice using Google's Gemini AI.",
        tools=[get_current_weather, get_weather_forecast],
        generate_content_config=types.GenerateContentConfig(
            temperature=0.7,  # Slightly creative for conversational responses
            max_output_tokens=1000,
            response_modalities=[types.Modality.TEXT],
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
            ]
        )
    )
    
    logger.info(f"Weather agent '{settings.agent_name}' created successfully")
    return weather_agent
