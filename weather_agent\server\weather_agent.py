"""
Simple Weather Agent using Google's Gemini AI.
"""

import logging
from google.adk.agents import Agent
from google.genai import types

from ..config.settings import settings, configure_google_auth

logger = logging.getLogger(__name__)


def create_weather_agent() -> Agent:
    """Create a simple Gemini weather agent."""

    # Configure Google authentication
    configure_google_auth()

    # Create simple weather agent with just Gemini LLM
    weather_agent = Agent(
        name=settings.agent_name,
        model="gemini-2.0-flash",
        instruction="""
You are a helpful weather assistant. You can provide weather information, forecasts,
and weather-related advice for any location. Be conversational and friendly.

When users ask about weather:
- Provide helpful responses about weather conditions
- Give general weather advice and recommendations
- Be conversational and engaging
- If you don't have real-time data, explain that you're providing general guidance

Example responses:
- "What's the weather like in San Francisco?" → Give general info about SF weather patterns
- "Should I bring an umbrella to London?" → Provide advice about London's typical weather
- "What's the forecast for Tokyo?" → Give general seasonal information for Tokyo
        """,
        description="A simple weather assistant powered by Google's Gemini AI",
        generate_content_config=types.GenerateContentConfig(
            temperature=0.7,
            max_output_tokens=1000,
            response_modalities=[types.Modality.TEXT]
        )
    )

    logger.info(f"Simple weather agent '{settings.agent_name}' created")
    return weather_agent
