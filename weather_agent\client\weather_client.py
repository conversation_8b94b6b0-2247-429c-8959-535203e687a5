"""
A2A Client for the Weather Agent.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, AsyncGenerator

import httpx
import structlog
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from a2a.types import (
    A2ARequest, SendMessageRequest, Message, Role, TextPart, Part,
    TaskResubscriptionRequest
)

from ..config.settings import settings

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer(),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)
console = Console()


class WeatherAgentClient:
    """A2A client for interacting with the Weather Agent."""
    
    def __init__(
        self, 
        agent_url: str = None,
        timeout: int = None,
        max_retries: int = None
    ):
        """
        Initialize the Weather Agent client.
        
        Args:
            agent_url: URL of the weather agent A2A endpoint
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
        """
        self.agent_url = agent_url or f"http://{settings.host}:{settings.port}{settings.a2a_rpc_url}"
        self.agent_card_url = agent_url or f"http://{settings.host}:{settings.port}{settings.a2a_agent_card_url}"
        self.timeout = timeout or settings.a2a_client_timeout
        self.max_retries = max_retries or settings.a2a_client_max_retries
        
        self.http_client = httpx.AsyncClient(
            timeout=self.timeout,
            headers={"Content-Type": "application/json"}
        )
        
        logger.info("Weather Agent client initialized", 
                   agent_url=self.agent_url,
                   timeout=self.timeout)
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()
    
    async def get_agent_card(self) -> Dict[str, Any]:
        """Retrieve the agent card from the weather agent."""
        try:
            response = await self.http_client.get(self.agent_card_url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error("Failed to retrieve agent card", error=str(e))
            raise
    
    async def send_weather_query(
        self, 
        query: str, 
        context_id: Optional[str] = None,
        stream: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Send a weather query to the agent and yield responses.
        
        Args:
            query: The weather query to send
            context_id: Optional context ID for conversation continuity
            stream: Whether to stream responses
            
        Yields:
            Response events from the weather agent
        """
        context_id = context_id or str(uuid.uuid4())
        task_id = str(uuid.uuid4())
        
        # Create the A2A message
        message = Message(
            messageId=str(uuid.uuid4()),
            role=Role.user,
            parts=[Part(root=TextPart(text=query))]
        )
        
        # Create the A2A request
        send_message_request = SendMessageRequest(
            id=str(uuid.uuid4()),
            contextId=context_id,
            taskId=task_id,
            message=message
        )
        
        a2a_request = A2ARequest(root=send_message_request)
        
        logger.info("Sending weather query", 
                   query=query, 
                   task_id=task_id,
                   context_id=context_id)
        
        try:
            # Send the request
            response = await self.http_client.post(
                self.agent_url,
                json=a2a_request.model_dump(),
                headers={"Accept": "text/event-stream" if stream else "application/json"}
            )
            response.raise_for_status()
            
            if stream:
                # Handle streaming response
                async for event in self._handle_streaming_response(response):
                    yield event
            else:
                # Handle single response
                response_data = response.json()
                yield response_data
                
        except Exception as e:
            logger.error("Failed to send weather query", 
                        error=str(e), 
                        query=query)
            raise
    
    async def _handle_streaming_response(
        self, 
        response: httpx.Response
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming server-sent events response."""
        async for line in response.aiter_lines():
            if line.startswith("data: "):
                try:
                    event_data = json.loads(line[6:])  # Remove "data: " prefix
                    yield event_data
                except json.JSONDecodeError:
                    logger.warning("Failed to parse streaming event", line=line)
                    continue
    
    async def ask_weather(self, query: str) -> str:
        """
        Simple method to ask a weather question and get a text response.
        
        Args:
            query: The weather question to ask
            
        Returns:
            The agent's text response
        """
        responses = []
        
        async for event in self.send_weather_query(query):
            if isinstance(event, dict):
                # Extract message from the event
                if "status" in event and "message" in event["status"]:
                    message = event["status"]["message"]
                    if "parts" in message:
                        for part in message["parts"]:
                            if "root" in part and "text" in part["root"]:
                                responses.append(part["root"]["text"])
        
        return "\n".join(responses) if responses else "No response received"


class WeatherClientCLI:
    """Command-line interface for the Weather Agent client."""
    
    def __init__(self):
        self.client = WeatherAgentClient()
        self.context_id = str(uuid.uuid4())
    
    async def run_interactive(self):
        """Run interactive CLI session."""
        console.print(Panel.fit(
            "[bold blue]🌤️  Weather Agent A2A Client[/bold blue]\n"
            "Powered by Google's Gemini AI\n\n"
            "Type your weather questions or 'quit' to exit.",
            title="Welcome"
        ))
        
        # Test connection
        try:
            agent_card = await self.client.get_agent_card()
            console.print(f"✅ Connected to: {agent_card.get('name', 'Weather Agent')}")
            console.print(f"📝 Description: {agent_card.get('description', 'N/A')}")
            console.print()
        except Exception as e:
            console.print(f"❌ Failed to connect to weather agent: {e}")
            return
        
        while True:
            try:
                # Get user input
                query = console.input("[bold green]🌍 Ask about weather:[/bold green] ")
                
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not query.strip():
                    continue
                
                # Show thinking indicator
                with console.status("[bold yellow]🤔 Thinking...[/bold yellow]"):
                    response = await self.client.ask_weather(query)
                
                # Display response
                console.print(Panel(
                    response,
                    title="🤖 Weather Agent Response",
                    border_style="blue"
                ))
                console.print()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                console.print(f"❌ Error: {e}")
                console.print()
        
        console.print("👋 Goodbye!")
        await self.client.close()
    
    async def run_examples(self):
        """Run example weather queries."""
        examples = [
            "What's the weather like in San Francisco?",
            "Will it rain tomorrow in New York?",
            "What's the forecast for London this weekend?",
            "Should I bring an umbrella to Tokyo?",
            "What's the weather forecast for the next 3 days in Paris?"
        ]
        
        console.print(Panel.fit(
            "[bold blue]🌤️  Weather Agent Examples[/bold blue]\n"
            "Running example weather queries...",
            title="Examples"
        ))
        
        for i, query in enumerate(examples, 1):
            console.print(f"\n[bold cyan]Example {i}:[/bold cyan] {query}")
            
            try:
                with console.status("[bold yellow]🤔 Processing...[/bold yellow]"):
                    response = await self.client.ask_weather(query)
                
                console.print(Panel(
                    response,
                    title="🤖 Response",
                    border_style="green"
                ))
                
                # Small delay between examples
                await asyncio.sleep(1)
                
            except Exception as e:
                console.print(f"❌ Error: {e}")
        
        await self.client.close()


async def main():
    """Main entry point for the client."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "examples":
        cli = WeatherClientCLI()
        await cli.run_examples()
    else:
        cli = WeatherClientCLI()
        await cli.run_interactive()


if __name__ == "__main__":
    asyncio.run(main())
