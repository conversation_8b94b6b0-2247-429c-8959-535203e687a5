"""
FastAPI A2A Server for the Weather Agent.
"""

import logging
import structlog
from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON><PERSON>ator

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from a2a.server.apps.jsonrpc.fastapi_app import A2AFastAPIApplication
from a2a.server.request_handlers.jsonrpc_handler import <PERSON><PERSON>ultR<PERSON><PERSON>H<PERSON>ler
from a2a.server.task_stores.in_memory_task_store import InMemoryTaskStore
from a2a.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill

from .weather_executor import WeatherAgentExecutor
from ..config.settings import settings

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer() if settings.log_format == "json" else structlog.dev.ConsoleRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """FastAPI lifespan context manager for startup and shutdown."""
    # Startup
    logger.info("Starting Weather Agent A2A Server", 
                host=settings.host, 
                port=settings.port,
                agent_name=settings.agent_name)
    
    yield
    
    # Shutdown
    logger.info("Shutting down Weather Agent A2A Server")


def create_agent_card() -> AgentCard:
    """Create the agent card for A2A protocol discovery."""
    
    agent_card = AgentCard(
        name=settings.agent_name,
        version="1.0.0",
        description="An intelligent weather assistant powered by Google's Gemini AI. Provides current weather conditions, forecasts, and weather-related advice for any location worldwide.",
        url=f"http://{settings.host}:{settings.port}{settings.a2a_rpc_url}",
        capabilities=AgentCapabilities(
            streaming=True,
            pushNotifications=False
        ),
        skills=[
            AgentSkill(
                id="weather_assistant",
                name="Weather Assistant",
                description="Provides weather information and advice using Gemini AI",
                tags=["weather", "gemini", "ai", "assistant"]
            )
        ],
        defaultInputModes=["text/plain"],
        defaultOutputModes=["text/plain", "application/json"]
    )
    
    return agent_card


def create_weather_app() -> FastAPI:
    """Create and configure the FastAPI application with A2A support."""
    
    # Create the main FastAPI app
    app = FastAPI(
        title="Weather Agent A2A Server",
        description="A2A-compliant weather agent powered by Google's Gemini AI",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Create agent executor and request handler
    weather_executor = WeatherAgentExecutor()
    task_store = InMemoryTaskStore()
    request_handler = DefaultRequestHandler(
        agent_executor=weather_executor,
        task_store=task_store
    )
    
    # Create agent card
    agent_card = create_agent_card()
    
    # Create A2A application
    a2a_app = A2AFastAPIApplication(
        agent_card=agent_card,
        http_handler=request_handler
    )
    
    # Add A2A routes to the main app
    a2a_app.add_routes_to_app(
        app,
        agent_card_url=settings.a2a_agent_card_url,
        rpc_url=settings.a2a_rpc_url,
        extended_agent_card_url=settings.a2a_extended_card_url
    )
    
    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "agent": settings.agent_name,
            "version": "1.0.0",
            "timestamp": "2025-01-01T00:00:00Z"
        }
    
    # Add info endpoint
    @app.get("/info")
    async def agent_info():
        """Agent information endpoint."""
        return {
            "name": settings.agent_name,
            "description": "Simple weather assistant powered by Google's Gemini AI",
            "version": "1.0.0",
            "type": "simple_gemini_agent",
            "ai_model": "gemini-2.0-flash",
            "capabilities": ["weather_assistance", "conversational_ai"],
            "a2a_endpoints": {
                "agent_card": settings.a2a_agent_card_url,
                "rpc": settings.a2a_rpc_url,
                "extended_card": settings.a2a_extended_card_url
            }
        }
    
    logger.info("Weather Agent A2A Server configured successfully")
    return app


def run_server():
    """Run the weather agent server."""
    
    # Configure logging level
    logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
    
    # Create the app
    app = create_weather_app()
    
    # Run with uvicorn
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=True,
        reload=False  # Set to True for development
    )


if __name__ == "__main__":
    run_server()
