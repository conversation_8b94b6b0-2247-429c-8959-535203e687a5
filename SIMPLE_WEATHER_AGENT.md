# Simple Weather Agent - Gemini + A2A

A simplified weather agent implementation that uses just Google's Gemini AI running in an A2A server.

## 🎯 What This Is

This is exactly what you asked for:
- **Simple Gemini-based LLM** using Google ADK Agent
- **Runs in A2A server** for agent-to-agent communication
- **No complex weather APIs** or mock data
- **Clean, minimal code** without unnecessary complexity

## 🏗️ Simple Architecture

```
┌─────────────────┐    A2A Protocol    ┌─────────────────┐
│   A2A Client    │ ◄─────────────────► │  Simple Weather │
│                 │                     │     Agent       │
└─────────────────┘                     └─────────────────┘
                                                │
                                                │ Direct API
                                                ▼
                                        ┌─────────────────┐
                                        │  Google Gemini  │
                                        │      AI         │
                                        └─────────────────┘
```

## 📁 Simplified Code Structure

### Core Files (Simplified):

1. **`weather_agent/server/weather_agent.py`** - Simple Gemini agent creation
   ```python
   def create_weather_agent() -> Agent:
       return Agent(
           name="weather_agent",
           model="gemini-2.0-flash",
           instruction="You are a helpful weather assistant...",
           # No complex tools or weather APIs
       )
   ```

2. **`weather_agent/server/weather_executor.py`** - Simple A2A executor
   ```python
   async def execute(self, context, event_queue):
       user_message = self._extract_user_message(context.message)
       agent = await self._get_agent()
       response = await agent.send_message(user_message)
       await self._publish_response(event_queue, context, response.text)
   ```

3. **`weather_agent/server/app.py`** - A2A FastAPI server (unchanged)

## 🚀 Quick Start

```bash
# 1. Install dependencies
uv sync

# 2. Add Google API key to .env
GOOGLE_API_KEY=your_key_here

# 3. Start server
uv run run_server.py

# 4. Test client
uv run run_client.py
```

## 💬 What the Agent Does

The simplified agent:
- ✅ Uses Google's Gemini AI directly
- ✅ Provides conversational weather advice
- ✅ Runs in A2A server for agent communication
- ✅ Handles weather-related questions naturally
- ❌ No complex weather API integrations
- ❌ No mock data or fake weather tools
- ❌ No unnecessary complexity

## 🎯 Example Interactions

```
User: "What's the weather like in San Francisco?"
Agent: "San Francisco typically has mild, Mediterranean-like weather with cool summers and mild winters. The city is known for its fog, especially in summer months..."

User: "Should I bring an umbrella to London?"
Agent: "Yes, it's always a good idea to bring an umbrella to London! The city is famous for its unpredictable weather and frequent light rain..."
```

## 🔧 Key Simplifications Made

### Before (Complex):
- Custom weather tools with mock APIs
- Complex weather data processing
- Location extraction logic
- Multiple weather response types
- 275+ lines of complex code

### After (Simple):
- Just Gemini AI agent
- Direct message passing
- Simple A2A integration
- ~50 lines of clean code
- Pure conversational AI approach

## 📝 Files Changed

1. **`weather_agent/server/weather_agent.py`** - Simplified to basic Gemini agent
2. **`weather_agent/server/weather_executor.py`** - Simplified to basic message passing
3. **Documentation** - Updated to reflect simple approach

## ✅ Benefits of Simple Approach

- **🎯 Focused**: Does exactly what you asked for
- **🧹 Clean**: No unnecessary complexity
- **🚀 Fast**: Direct Gemini AI integration
- **🔧 Maintainable**: Easy to understand and modify
- **📡 A2A Ready**: Full agent-to-agent communication support

This is now a truly simple Gemini-based LLM weather agent running in an A2A server, exactly as requested!
