# A2A Python SDK Weather Agent Dependencies

# Core A2A SDK with Google AI integration
google-adk[a2a]>=1.0.0

# A2A Protocol SDK
a2a-sdk>=0.2.7

# Google AI/Gemini integration
google-genai>=1.21.1
google-cloud-aiplatform>=1.95.1

# Authentication and Google Cloud
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.2.0

# FastAPI and server dependencies
fastapi>=0.115.0
uvicorn>=0.34.0
starlette>=0.46.2

# HTTP client for weather API and general requests
httpx>=0.27.0
requests>=2.32.4
aiohttp>=3.8.0

# Environment and configuration
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Async support
anyio>=4.9.0

# Logging and utilities
structlog>=24.1.0
rich>=13.0.0

# Development dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
ruff>=0.1.0
