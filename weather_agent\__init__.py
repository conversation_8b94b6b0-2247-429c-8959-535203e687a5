# Weather Agent Package
"""
A2A Weather Agent implementation using Google's Gemini AI models.

This package provides:
- Weather Agent Server with A2A protocol support
- Google Gemini AI integration for intelligent weather responses
- FastAPI-based server implementation
- A2A client for testing and integration
"""

__version__ = "1.0.0"
__author__ = "A2A Weather Agent"

from .config.settings import settings
from .server.weather_agent import create_weather_agent
from .server.weather_executor import WeatherAgentExecutor
from .server.app import create_weather_app, run_server
from .client.weather_client import WeatherAgent<PERSON>lient, WeatherClientCLI

__all__ = [
    "settings",
    "create_weather_agent",
    "WeatherAgentExecutor",
    "create_weather_app",
    "run_server",
    "WeatherAgentClient",
    "WeatherClientCLI"
]
