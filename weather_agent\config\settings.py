"""
Configuration settings for the Weather Agent.
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class WeatherAgentSettings(BaseSettings):
    """Configuration settings for the Weather Agent."""
    
    # Google AI Configuration
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")
    google_genai_use_vertexai: bool = Field(False, env="GOOGLE_GENAI_USE_VERTEXAI")
    google_cloud_project: Optional[str] = Field(None, env="GOOGLE_CLOUD_PROJECT")
    google_cloud_location: Optional[str] = Field(None, env="GOOGLE_CLOUD_LOCATION")
    
    # Weather API Configuration (optional)
    weather_api_key: Optional[str] = Field(None, env="WEATHER_API_KEY")
    weather_api_base_url: str = Field(
        "https://api.openweathermap.org/data/2.5", 
        env="WEATHER_API_BASE_URL"
    )
    
    # Server Configuration
    host: str = Field("127.0.0.1", env="WEATHER_AGENT_HOST")
    port: int = Field(8001, env="WEATHER_AGENT_PORT")
    agent_name: str = Field("weather_agent", env="WEATHER_AGENT_NAME")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")
    
    # A2A Protocol Configuration
    a2a_agent_card_url: str = Field("/.well-known/agent.json", env="A2A_AGENT_CARD_URL")
    a2a_rpc_url: str = Field("/a2a/weather_agent", env="A2A_RPC_URL")
    a2a_extended_card_url: str = Field(
        "/agent/authenticatedExtendedCard", 
        env="A2A_EXTENDED_CARD_URL"
    )
    
    # A2A Client Configuration
    a2a_client_timeout: int = Field(30, env="A2A_CLIENT_TIMEOUT")
    a2a_client_max_retries: int = Field(3, env="A2A_CLIENT_MAX_RETRIES")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = WeatherAgentSettings()


def configure_google_auth():
    """Configure Google authentication based on settings."""
    if settings.google_genai_use_vertexai:
        # Configure for Vertex AI
        if settings.google_cloud_project:
            os.environ["GOOGLE_CLOUD_PROJECT"] = settings.google_cloud_project
        if settings.google_cloud_location:
            os.environ["GOOGLE_CLOUD_LOCATION"] = settings.google_cloud_location
        os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "true"
    else:
        # Configure for Google AI Studio API
        os.environ["GOOGLE_API_KEY"] = settings.google_api_key
        # Ensure Vertex AI is disabled
        os.environ.pop("GOOGLE_GENAI_USE_VERTEXAI", None)
