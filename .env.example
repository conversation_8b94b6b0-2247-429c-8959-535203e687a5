# Google AI API Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Alternative: Vertex AI Configuration (for production)
# GOOGLE_GENAI_USE_VERTEXAI=true
# GOOGLE_CLOUD_PROJECT=your_project_id
# GOOGLE_CLOUD_LOCATION=us-central1

# Weather API Configuration (optional - for real weather data)
# Get a free API key from: https://openweathermap.org/api
WEATHER_API_KEY=your_weather_api_key_here
WEATHER_API_BASE_URL=https://api.openweathermap.org/data/2.5

# Server Configuration
WEATHER_AGENT_HOST=127.0.0.1
WEATHER_AGENT_PORT=8001
WEATHER_AGENT_NAME=weather_agent

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# A2A Protocol Configuration
A2A_AGENT_CARD_URL=/.well-known/agent.json
A2A_RPC_URL=/a2a/weather_agent
A2A_EXTENDED_CARD_URL=/agent/authenticatedExtendedCard

# A2A Client Configuration
A2A_CLIENT_TIMEOUT=30
A2A_CLIENT_MAX_RETRIES=3
