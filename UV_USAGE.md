# Using UV with Weather Agent

This guide shows how to use the `uv` package manager with the Weather Agent A2A Server.

## 🚀 Why UV?

`uv` is a fast Python package manager and project manager written in Rust. It provides:

- **⚡ Speed**: 10-100x faster than pip
- **🔒 Reliability**: Consistent dependency resolution
- **🎯 Simplicity**: Single tool for package and project management
- **🔄 Compatibility**: Drop-in replacement for pip

## 📦 Installation

### Install UV

```bash
# On macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Or using pip
pip install uv
```

### Verify Installation

```bash
uv --version
```

## 🛠️ Weather Agent Setup with UV

### 1. Install Dependencies

```bash
# Install all dependencies from requirements.txt
uv sync

# Or install specific packages
uv add google-adk[a2a] fastapi uvicorn rich
```

### 2. Run the Weather Agent

#### Start Server

```bash
# Using uv run (recommended)
uv run run_server.py

# Or make executable and run directly
chmod +x run_server.py
./run_server.py
```

#### Start Client

```bash
# Interactive client
uv run run_client.py

# Run examples
uv run run_client.py examples

# Or make executable
chmod +x run_client.py
./run_client.py
```

#### Using Main Module

```bash
# Server
uv run -m weather_agent.main server

# Client
uv run -m weather_agent.main client

# Info
uv run -m weather_agent.main info
```

### 3. Development Commands

```bash
# Install development dependencies
uv add --dev pytest black isort mypy

# Run tests
uv run pytest

# Format code
uv run black weather_agent/
uv run isort weather_agent/

# Type checking
uv run mypy weather_agent/
```

## 🔧 UV Commands Reference

### Package Management

```bash
# Add a package
uv add package-name

# Add development dependency
uv add --dev package-name

# Remove a package
uv remove package-name

# Update all packages
uv sync --upgrade

# Install from requirements.txt
uv pip install -r requirements.txt
```

### Running Scripts

```bash
# Run a Python script
uv run script.py

# Run a module
uv run -m module.name

# Run with specific Python version
uv run --python 3.11 script.py
```

### Virtual Environment

```bash
# Create virtual environment
uv venv

# Activate virtual environment (Linux/macOS)
source .venv/bin/activate

# Activate virtual environment (Windows)
.venv\Scripts\activate

# Run without activating
uv run script.py
```

## 🎯 Weather Agent Specific Commands

### Quick Start

```bash
# 1. Install dependencies
uv sync

# 2. Configure environment
cp .env.example .env
# Edit .env with your Google API key

# 3. Start server (in one terminal)
uv run run_server.py

# 4. Start client (in another terminal)
uv run run_client.py
```

### Development Workflow

```bash
# Install with dev dependencies
uv sync --dev

# Run server with debug logging
uv run -m weather_agent.main server --log-level DEBUG

# Run client examples
uv run -m weather_agent.main client --examples

# Check agent info
uv run -m weather_agent.main info
```

### Custom Server Configuration

```bash
# Custom host and port
uv run -m weather_agent.main server --host 0.0.0.0 --port 8002

# With environment override
WEATHER_AGENT_PORT=8003 uv run run_server.py
```

## 🐛 Troubleshooting

### Common Issues

1. **UV not found**
   ```bash
   # Restart terminal after installation or add to PATH
   export PATH="$HOME/.cargo/bin:$PATH"
   ```

2. **Permission denied on scripts**
   ```bash
   # Make scripts executable
   chmod +x run_server.py run_client.py
   ```

3. **Module not found**
   ```bash
   # Ensure dependencies are installed
   uv sync
   ```

### Performance Tips

- Use `uv run` instead of activating virtual environments
- Use `uv sync` instead of `pip install -r requirements.txt`
- Use `uv add` instead of `pip install` for new packages

## 📚 Additional Resources

- [UV Documentation](https://docs.astral.sh/uv/)
- [UV GitHub Repository](https://github.com/astral-sh/uv)
- [Weather Agent Documentation](WEATHER_AGENT_README.md)
- [Setup Guide](SETUP.md)

---

**Fast, reliable Python package management with UV! ⚡**
