# Weather Agent - A2A Python SDK with Google Gemini AI

A complete implementation of an intelligent weather agent using the A2A (Agent2Agent) Python SDK integrated with Google's Gemini AI models. This agent can process weather-related queries and provide conversational responses while maintaining full compliance with the A2A protocol for agent-to-agent communication.

## 🌟 Features

- **🤖 Gemini AI Integration**: Powered by Google's advanced Gemini AI models for intelligent weather responses
- **🌐 A2A Protocol Compliance**: Full implementation of the Agent2Agent protocol for seamless agent communication
- **🔐 Secure Authentication**: Built-in API key authentication using A2A security schemes
- **⚡ Async Processing**: Non-blocking request handling with proper task management
- **📊 Rich CLI Interface**: Interactive command-line client with beautiful formatting
- **🛠️ Production Ready**: Comprehensive error handling, logging, and configuration management
- **📖 Comprehensive Documentation**: Complete setup instructions and examples

## 🏗️ Architecture

```
┌─────────────────┐    A2A Protocol    ┌─────────────────┐
│   A2A Client    │ ◄─────────────────► │  Weather Agent  │
│                 │                     │     Server      │
└─────────────────┘                     └─────────────────┘
                                                │
                                                │ Gemini API
                                                ▼
                                        ┌─────────────────┐
                                        │  Google Gemini  │
                                        │   AI Service    │
                                        └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- Google AI API key (get one at [Google AI Studio](https://makersuite.google.com/app/apikey))
- pip or uv package manager

### Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Google AI API key:
   ```env
   GOOGLE_API_KEY=your_google_ai_api_key_here
   ```

### Running the Weather Agent

1. **Start the server**:
   ```bash
   python run_server.py
   ```
   
   The server will start on `http://localhost:8000` by default.

2. **In another terminal, run the client**:
   ```bash
   python run_client.py
   ```

3. **Start asking weather questions**!

## 📋 Example Queries

Try these example weather queries with the agent:

- "What's the weather like in San Francisco?"
- "Will it rain tomorrow in New York?"
- "What should I wear for a 20°C sunny day?"
- "Explain what causes thunderstorms"
- "What's the difference between weather and climate?"
- "Give me a 5-day forecast for London"
- "Is it a good day for outdoor activities in Miami?"
- "What are the best weather conditions for hiking?"

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_API_KEY` | Google AI API key (required) | - |
| `WEATHER_AGENT_HOST` | Server host address | `localhost` |
| `WEATHER_AGENT_PORT` | Server port number | `8000` |
| `WEATHER_AGENT_NAME` | Agent name | `WeatherBot` |
| `WEATHER_AGENT_VERSION` | Agent version | `1.0.0` |
| `WEATHER_AGENT_LOG_LEVEL` | Logging level | `INFO` |
| `WEATHER_AGENT_GEMINI_MODEL` | Gemini model to use | `gemini-2.0-flash-exp` |
| `WEATHER_AGENT_GEMINI_TEMPERATURE` | Response creativity (0.0-1.0) | `0.7` |

### Agent Capabilities

The weather agent supports the following skills:

- **Current Weather**: Get current weather conditions for any location
- **Weather Forecast**: Get weather forecasts and predictions  
- **Weather Advice**: Provide weather-related advice and recommendations
- **Weather Explanation**: Explain weather phenomena and conditions

## 🔐 Authentication

The agent uses API key authentication through the A2A protocol:

- **Security Scheme**: API Key in Header
- **Header Name**: `X-API-KEY`
- **Client Setup**: Automatic through A2A SDK authentication interceptors

## 🌐 API Endpoints

When the server is running, these endpoints are available:

- `GET /.well-known/agent.json` - Agent card (capabilities and metadata)
- `POST /` - A2A JSON-RPC endpoint for agent communication
- `GET /docs` - Interactive API documentation
- `GET /redoc` - Alternative API documentation

## 📁 Project Structure

```
weather-agent/
├── weather_agent/
│   ├── __init__.py          # Package initialization
│   ├── config.py            # Configuration management
│   ├── executor.py          # Weather agent executor with Gemini integration
│   ├── server.py            # A2A server implementation
│   └── client.py            # A2A client implementation
├── run_server.py            # Server entry point
├── run_client.py            # Client entry point
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
└── WEATHER_AGENT_README.md  # This documentation
```

## 🧪 Testing the Agent

### Manual Testing

1. Start the server: `python run_server.py`
2. Test the agent card endpoint:
   ```bash
   curl http://localhost:8000/.well-known/agent.json
   ```
3. Use the interactive client: `python run_client.py`

### A2A Inspector

You can validate your agent using the [A2A Inspector](https://github.com/a2aproject/a2a-inspector):

```bash
# Install the inspector
npm install -g @a2aproject/inspector

# Validate your agent
a2a-inspector validate http://localhost:8000
```

## 🔍 Troubleshooting

### Common Issues

1. **"GOOGLE_API_KEY environment variable is required"**
   - Make sure you've set your Google AI API key in the `.env` file
   - Verify the `.env` file is in the project root directory

2. **"Failed to configure Gemini client"**
   - Check that your Google AI API key is valid
   - Ensure you have internet connectivity
   - Verify the Gemini model name is correct

3. **"Connection refused" when running client**
   - Make sure the server is running first
   - Check that the server is listening on the correct host/port
   - Verify firewall settings if running on different machines

### Logging

The agent uses structured logging. To increase verbosity:

```bash
export WEATHER_AGENT_LOG_LEVEL=DEBUG
python run_server.py
```

## 🤝 Contributing

This is a complete example implementation. To extend or modify:

1. **Add new skills**: Modify the `_create_agent_card()` method in `server.py`
2. **Enhance responses**: Update the system prompt in `executor.py`
3. **Add authentication**: Extend the security schemes in the agent card
4. **Integrate weather APIs**: Add real-time weather data sources to the executor

## 📄 License

This project is provided as an example implementation for the A2A Python SDK. See the [A2A Python SDK license](https://github.com/a2aproject/a2a-python/blob/main/LICENSE) for details.

## 🔗 Related Links

- [A2A Protocol Documentation](https://a2aproject.github.io/A2A/)
- [A2A Python SDK](https://github.com/a2aproject/a2a-python)
- [Google AI Studio](https://makersuite.google.com/)
- [A2A Inspector](https://github.com/a2aproject/a2a-inspector)
