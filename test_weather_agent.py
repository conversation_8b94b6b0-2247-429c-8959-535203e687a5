#!/usr/bin/env python3
"""Test script for the Weather Agent."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

from weather_agent.client import WeatherAgentClient
from weather_agent.config import config

console = Console()


async def test_agent_connection():
    """Test basic connection to the weather agent."""
    console.print("🔗 Testing agent connection...")
    
    try:
        agent_url = f"http://{config.host}:{config.port}"
        client = WeatherAgentClient(agent_url)
        await client.initialize()
        
        console.print("✅ Successfully connected to weather agent")
        
        # Display agent info
        if client.agent_card:
            table = Table(title="Agent Information")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="white")
            
            table.add_row("Name", client.agent_card.name)
            table.add_row("Version", client.agent_card.version)
            table.add_row("Skills", str(len(client.agent_card.skills)))
            
            console.print(table)
        
        await client.close()
        return True
        
    except Exception as e:
        console.print(f"❌ Connection failed: {e}")
        return False


async def test_weather_queries():
    """Test various weather queries."""
    console.print("\n🌤️  Testing weather queries...")
    
    test_queries = [
        "What's the weather like in San Francisco?",
        "Explain what causes rain",
        "What should I wear when it's 15°C and cloudy?",
        "Is it good weather for a picnic today?"
    ]
    
    try:
        agent_url = f"http://{config.host}:{config.port}"
        client = WeatherAgentClient(agent_url)
        await client.initialize()
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            console.print(f"Query {i}: {query}")
            
            try:
                response = await client.send_weather_query(query)
                success = len(response) > 0 and "error" not in response.lower()
                results.append((query, success, response[:100] + "..." if len(response) > 100 else response))
                
                if success:
                    console.print("✅ Response received")
                else:
                    console.print("❌ Invalid response")
                    
            except Exception as e:
                console.print(f"❌ Query failed: {e}")
                results.append((query, False, str(e)))
        
        await client.close()
        
        # Display results
        console.print("\n📊 Test Results:")
        table = Table()
        table.add_column("Query", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Response Preview", style="dim")
        
        for query, success, response in results:
            status = "✅ Pass" if success else "❌ Fail"
            table.add_row(query[:50] + "...", status, response)
        
        console.print(table)
        
        success_count = sum(1 for _, success, _ in results if success)
        console.print(f"\n📈 Success Rate: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        
        return success_count == len(results)
        
    except Exception as e:
        console.print(f"❌ Testing failed: {e}")
        return False


async def test_agent_card():
    """Test agent card endpoint."""
    console.print("\n📋 Testing agent card endpoint...")
    
    try:
        agent_url = f"http://{config.host}:{config.port}/.well-known/agent.json"
        
        async with httpx.AsyncClient() as client:
            response = await client.get(agent_url)
            
            if response.status_code == 200:
                agent_data = response.json()
                console.print("✅ Agent card retrieved successfully")
                
                # Validate required fields
                required_fields = ["name", "description", "version", "skills", "capabilities"]
                missing_fields = [field for field in required_fields if field not in agent_data]
                
                if missing_fields:
                    console.print(f"⚠️  Missing fields: {', '.join(missing_fields)}")
                    return False
                
                console.print(f"   Name: {agent_data.get('name')}")
                console.print(f"   Version: {agent_data.get('version')}")
                console.print(f"   Skills: {len(agent_data.get('skills', []))}")
                
                return True
            else:
                console.print(f"❌ HTTP {response.status_code}: {response.text}")
                return False
                
    except Exception as e:
        console.print(f"❌ Agent card test failed: {e}")
        return False


async def main():
    """Main test function."""
    console.print(Panel.fit("🧪 Weather Agent Test Suite", style="bold blue"))
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        console.print("⚠️  python-dotenv not available, using system environment")
    
    # Check if server is likely running
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://{config.host}:{config.port}/.well-known/agent.json", timeout=5)
            if response.status_code != 200:
                console.print("⚠️  Server may not be running. Start it with: python run_server.py")
                return
    except Exception:
        console.print("❌ Cannot connect to server. Make sure it's running with: python run_server.py")
        return
    
    # Run tests
    tests = [
        ("Agent Card", test_agent_card),
        ("Agent Connection", test_agent_connection),
        ("Weather Queries", test_weather_queries),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    console.print(f"\n{'='*50}")
    console.print("📊 Test Summary:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        console.print(f"   {test_name}: {status}")
    
    console.print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        console.print("🎉 All tests passed! Your weather agent is working correctly.")
    else:
        console.print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    asyncio.run(main())
