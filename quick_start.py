#!/usr/bin/env python3
"""Quick start script for the Weather Agent."""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print the welcome banner."""
    print("🌤️" * 20)
    print("🌤️  WEATHER AGENT - QUICK START  🌤️")
    print("🌤️" * 20)
    print()

def check_requirements():
    """Check if basic requirements are met."""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 10):
        print(f"❌ Python 3.10+ required. Current: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check if .env exists
    if not Path(".env").exists():
        print("⚠️  .env file not found")
        if Path(".env.example").exists():
            print("📝 Creating .env from template...")
            Path(".env").write_text(Path(".env.example").read_text())
            print("✅ Created .env file")
        else:
            print("❌ No .env.example found")
            return False
    
    # Check for Google API key
    env_content = Path(".env").read_text()
    if "GOOGLE_API_KEY=your_google_ai_api_key_here" in env_content:
        print("⚠️  Please set your GOOGLE_API_KEY in .env file")
        print("   Get your key at: https://makersuite.google.com/app/apikey")
        return False
    elif "GOOGLE_API_KEY=" not in env_content:
        print("❌ GOOGLE_API_KEY not found in .env")
        return False
    
    print("✅ Environment configured")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Dependencies installed")
            return True
        else:
            print(f"❌ Installation failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Installation timed out")
        return False
    except Exception as e:
        print(f"❌ Installation error: {e}")
        return False

def start_server():
    """Start the weather agent server."""
    print("\n🚀 Starting Weather Agent server...")
    
    try:
        # Start server in background
        process = subprocess.Popen([
            sys.executable, "run_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Server started successfully!")
            print("   Server URL: http://localhost:8000")
            print("   API Docs: http://localhost:8000/docs")
            print("   Agent Card: http://localhost:8000/.well-known/agent.json")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Server failed to start:")
            print(f"   stdout: {stdout.decode()}")
            print(f"   stderr: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def run_client():
    """Run the interactive client."""
    print("\n🖥️  Starting interactive client...")
    print("   (Press Ctrl+C to exit)")
    print()
    
    try:
        subprocess.run([sys.executable, "run_client.py"])
    except KeyboardInterrupt:
        print("\n👋 Client stopped")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main quick start function."""
    print_banner()
    
    # Step 1: Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        return
    
    # Step 2: Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed.")
        return
    
    # Step 3: Start server
    server_process = start_server()
    if not server_process:
        print("\n❌ Failed to start server.")
        return
    
    try:
        # Step 4: Run client
        print("\n" + "="*50)
        print("🎉 Setup complete! Starting interactive client...")
        print("="*50)
        
        run_client()
        
    finally:
        # Clean up: stop server
        if server_process and server_process.poll() is None:
            print("\n🛑 Stopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
                print("✅ Server stopped")
            except subprocess.TimeoutExpired:
                server_process.kill()
                print("⚠️  Server force-killed")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Quick start interrupted")
    except Exception as e:
        print(f"\n❌ Quick start failed: {e}")
        sys.exit(1)
