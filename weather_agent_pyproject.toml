[project]
name = "weather-agent-a2a"
version = "1.0.0"
description = "Weather Agent A2A Server powered by Google's Gemini AI"
authors = [
    {name = "A2A Weather Agent", email = "<EMAIL>"}
]
readme = "WEATHER_AGENT_README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
keywords = ["a2a", "weather", "agent", "gemini", "ai", "fastapi"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # A2A and Google AI dependencies
    "google-adk[a2a]>=1.0.0",
    "a2a-sdk>=0.2.7",
    "google-genai>=1.21.1",
    "google-auth>=2.23.0",
    "google-auth-oauthlib>=1.1.0",
    "google-auth-httplib2>=0.2.0",
    
    # FastAPI and server dependencies
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "starlette>=0.27.0",
    
    # Configuration and validation
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    
    # HTTP client and async support
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    
    # Logging and monitoring
    "structlog>=23.2.0",
    
    # CLI and user interface
    "rich>=13.7.0",
    "click>=8.1.0",
    
    # Utilities
    "python-multipart>=0.0.6",
    "typing-extensions>=4.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
]

[project.urls]
Homepage = "https://github.com/a2aproject/weather-agent-a2a"
Documentation = "https://github.com/a2aproject/weather-agent-a2a/blob/main/WEATHER_AGENT_README.md"
Repository = "https://github.com/a2aproject/weather-agent-a2a"
Issues = "https://github.com/a2aproject/weather-agent-a2a/issues"

[project.scripts]
weather-agent = "weather_agent.main:main"
weather-server = "weather_agent.server.app:run_server"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["weather_agent"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["weather_agent"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "google.*",
    "a2a.*",
    "structlog.*",
    "rich.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["weather_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
