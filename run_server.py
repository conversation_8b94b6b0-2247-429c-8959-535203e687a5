#!/usr/bin/env python3
"""
Simple script to run the Weather Agent A2A Server.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from weather_agent.server.app import run_server

if __name__ == "__main__":
    print("🌤️  Starting Weather Agent A2A Server...")
    print("🤖 Powered by Google's Gemini AI")
    print("📡 A2A Protocol Enabled")
    print()

    try:
        run_server()
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)
