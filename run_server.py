#!/usr/bin/env python3
"""Entry point for running the Weather Agent server."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from weather_agent.server import get_app
from weather_agent.config import config

import uvicorn
import structlog

logger = structlog.get_logger(__name__)


def main():
    """Main entry point for the server."""
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Validate configuration
        if not config.google_api_key:
            logger.error("GOOGLE_API_KEY environment variable is required")
            sys.exit(1)
        
        # Get the FastAPI app
        app = get_app()
        
        logger.info(f"Starting Weather Agent server on {config.host}:{config.port}")
        logger.info(f"Agent: {config.agent_name} v{config.agent_version}")
        logger.info(f"Gemini Model: {config.gemini_model}")
        logger.info(f"API Documentation: http://{config.host}:{config.port}/docs")
        logger.info(f"Agent Card: http://{config.host}:{config.port}/.well-known/agent.json")
        
        # Run the server
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            log_level=config.log_level.lower(),
            access_log=True,
            reload=False
        )
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
